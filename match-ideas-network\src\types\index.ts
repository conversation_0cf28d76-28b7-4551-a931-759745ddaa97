
export type User = {
  id: string;
  email: string;
  name: string;
  skills: string;
};

export type Idea = {
  id: string;
  title: string;
  description: string;
  category: 'Tech' | 'Health' | 'Education' | 'Other';
  skillsNeeded: string;
  ownerId: string;
  ownerName?: string;
  createdAt?: number;
};

export type Swipe = {
  userId: string;
  ideaId: string;
  direction: 'left' | 'right';
};

export type Match = {
  id: string;
  user1Id: string;
  user2Id: string;
  ideaId: string;
  ideaTitle?: string;
  userName?: string;
};

export type Message = {
  id: string;
  senderId: string;
  receiverId: string;
  text: string;
  timestamp: number;
};
