
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useIdea } from "@/contexts/idea-context";
import { useAuth } from "@/contexts/auth-context";
import { PlusCircle, MessageCircle, User, Send, Lightbulb, ThumbsUp, Users, Calendar, Clock, ArrowRight, Sparkles } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Match, Message } from "@/types";
import { motion } from "framer-motion";

export default function Dashboard() {
  const { userIdeas, likedIdeas, matches, sendMessage, getMessagesForMatch } = useIdea();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [selectedTab, setSelectedTab] = useState("my-ideas");
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [chatOpen, setChatOpen] = useState(false);
  const [messageText, setMessageText] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);

  const handleOpenChat = (match: Match) => {
    setSelectedMatch(match);
    const matchMessages = getMessagesForMatch(match.id);
    setMessages(matchMessages);
    setChatOpen(true);
  };

  const handleSendMessage = async () => {
    if (!selectedMatch || !messageText.trim()) return;
    
    try {
      await sendMessage(selectedMatch.id, messageText);
      setMessageText("");
      
      // Refresh messages
      const updatedMessages = getMessagesForMatch(selectedMatch.id);
      setMessages(updatedMessages);
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString([], {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };
  
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  return (
    <div className="container mx-auto px-4 py-12 max-w-6xl">
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-10"
      >
        <h1 className="text-3xl md:text-4xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">Your Dashboard</h1>
        <p className="text-gray-600 max-w-2xl">Manage your ideas, view your liked ideas, and connect with your matches</p>
      </motion.div>
      
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-10 p-1">
          <TabsTrigger value="my-ideas" className="text-sm md:text-base py-3">
            <Lightbulb className="h-4 w-4 mr-2" />
            My Ideas
          </TabsTrigger>
          <TabsTrigger value="liked-ideas" className="text-sm md:text-base py-3">
            <ThumbsUp className="h-4 w-4 mr-2" />
            Liked Ideas
          </TabsTrigger>
          <TabsTrigger value="matches" className="text-sm md:text-base py-3">
            <Users className="h-4 w-4 mr-2" />
            Matches
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-ideas" className="space-y-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-800">Your Ideas</h2>
            <Button onClick={() => navigate("/submit")} className="gap-2 rounded-xl">
              <PlusCircle size={18} />
              Submit New Idea
            </Button>
          </div>
          
          {isLoading ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="opacity-60 animate-pulse">
                  <CardHeader className="pb-2">
                    <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-5 bg-gray-200 rounded w-1/4"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="flex gap-1">
                      <div className="h-5 bg-gray-200 rounded w-16"></div>
                      <div className="h-5 bg-gray-200 rounded w-16"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : userIdeas.length === 0 ? (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="border-dashed border-2 border-gray-200">
                <CardContent className="pt-10 pb-10 text-center">
                  <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                    <Lightbulb className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">No ideas yet</h3>
                  <p className="mb-6 text-gray-500 max-w-md mx-auto">You haven't submitted any ideas yet. Share your brilliant concept and find the perfect co-founder!</p>
                  <Button 
                    onClick={() => navigate("/submit")} 
                    size="lg"
                    className="gap-2 px-6 py-5 rounded-xl shadow-md hover:shadow-lg transition-all hover:-translate-y-1"
                  >
                    Submit Your First Idea <ArrowRight size={18} />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {userIdeas.map((idea, index) => (
                <motion.div
                  key={idea.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full flex flex-col hover:shadow-md transition-shadow overflow-hidden border-gray-200">
                    <div className="h-1 bg-gradient-to-r from-primary/20 via-primary to-primary/20"></div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start gap-2">
                        <CardTitle className="text-xl font-bold">{idea.title}</CardTitle>
                        <Badge className="text-xs font-medium px-2 py-1 bg-primary/10 text-primary hover:bg-primary/20">
                          {idea.category}
                        </Badge>
                      </div>
                      <CardDescription className="text-xs flex items-center gap-1 mt-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(idea.createdAt || Date.now()).toLocaleDateString()}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-grow">
                      <p className="mb-4 text-gray-600 text-sm leading-relaxed">{idea.description}</p>
                      <div>
                        <h3 className="text-xs font-medium mb-2 flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          <span>Skills Needed:</span>
                        </h3>
                        <div className="flex flex-wrap gap-1.5">
                          {idea.skillsNeeded.split(",").map((skill, index) => (
                            <Badge 
                              key={index} 
                              variant="outline" 
                              className="text-xs bg-gray-50 text-gray-700 hover:bg-gray-100 transition-colors"
                            >
                              {skill.trim()}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="pt-2 border-t text-xs text-gray-500 flex justify-between">
                      <div>ID: {idea.id.substring(0, 8)}...</div>
                      <div className="flex items-center gap-1">
                        <Sparkles className="h-3 w-3 text-yellow-500" />
                        Active
                      </div>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="liked-ideas" className="space-y-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-800">Ideas You've Liked</h2>
            <Button 
              onClick={() => navigate("/swipe")} 
              variant="outline" 
              className="gap-2 rounded-xl"
            >
              <ThumbsUp size={16} />
              Find More Ideas
            </Button>
          </div>
          
          {isLoading ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="opacity-60 animate-pulse">
                  <CardHeader className="pb-2">
                    <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-5 bg-gray-200 rounded w-1/4"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="flex gap-1">
                      <div className="h-5 bg-gray-200 rounded w-16"></div>
                      <div className="h-5 bg-gray-200 rounded w-16"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : likedIdeas.length === 0 ? (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="border-dashed border-2 border-gray-200">
                <CardContent className="pt-10 pb-10 text-center">
                  <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <ThumbsUp className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">No liked ideas yet</h3>
                  <p className="mb-6 text-gray-500 max-w-md mx-auto">You haven't liked any ideas yet. Start swiping to discover ideas that match your interests!</p>
                  <Button 
                    onClick={() => navigate("/swipe")} 
                    variant="outline"
                    size="lg"
                    className="gap-2 px-6 py-5 rounded-xl hover:bg-primary/5 transition-all hover:-translate-y-1"
                  >
                    Discover Ideas <ArrowRight size={18} />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {likedIdeas.map((idea, index) => (
                <motion.div
                  key={idea.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full flex flex-col hover:shadow-md transition-shadow overflow-hidden border-gray-200">
                    <div className="h-1 bg-gradient-to-r from-green-300 via-green-500 to-green-300"></div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start gap-2">
                        <CardTitle className="text-xl font-bold">{idea.title}</CardTitle>
                        <Badge className="text-xs font-medium px-2 py-1 bg-green-100 text-green-700 hover:bg-green-200">
                          {idea.category}
                        </Badge>
                      </div>
                      <CardDescription className="text-xs flex items-center gap-1 mt-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(idea.createdAt || Date.now()).toLocaleDateString()}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-grow">
                      <p className="mb-4 text-gray-600 text-sm leading-relaxed">{idea.description}</p>
                      <div className="mb-3">
                        <h3 className="text-xs font-medium mb-2 flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          <span>Skills Needed:</span>
                        </h3>
                        <div className="flex flex-wrap gap-1.5">
                          {idea.skillsNeeded.split(",").map((skill, index) => (
                            <Badge 
                              key={index} 
                              variant="outline" 
                              className="text-xs bg-gray-50 text-gray-700 hover:bg-gray-100 transition-colors"
                            >
                              {skill.trim()}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="pt-2 border-t">
                      {idea.ownerName && (
                        <div className="flex items-center text-xs text-gray-500 w-full">
                          <Avatar className="h-6 w-6 mr-2">
                            <AvatarFallback className="text-[10px] bg-primary/10 text-primary">
                              {getInitials(idea.ownerName)}
                            </AvatarFallback>
                          </Avatar>
                          By {idea.ownerName}
                        </div>
                      )}
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="matches" className="space-y-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-800">Your Matches</h2>
            <Button 
              onClick={() => navigate("/swipe")} 
              variant="outline" 
              className="gap-2 rounded-xl"
            >
              <Users size={16} />
              Find More Matches
            </Button>
          </div>
          
          {isLoading ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2].map((i) => (
                <Card key={i} className="opacity-60 animate-pulse">
                  <CardHeader className="pb-2">
                    <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="h-10 bg-gray-200 rounded w-full"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : matches.length === 0 ? (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="border-dashed border-2 border-gray-200">
                <CardContent className="pt-10 pb-10 text-center">
                  <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <Users className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">No matches yet</h3>
                  <p className="mb-6 text-gray-500 max-w-md mx-auto">
                    When you and another user both like each other's ideas, you'll see a match here. Keep swiping to find your perfect co-founder!
                  </p>
                  <Button 
                    onClick={() => navigate("/swipe")} 
                    variant="outline"
                    size="lg"
                    className="gap-2 px-6 py-5 rounded-xl hover:bg-primary/5 transition-all hover:-translate-y-1"
                  >
                    Discover More Ideas <ArrowRight size={18} />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {matches.map((match, index) => (
                <motion.div
                  key={match.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-md transition-shadow overflow-hidden border-gray-200">
                    <div className="h-1 bg-gradient-to-r from-primary/20 via-primary to-primary/20"></div>
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10 border-2 border-primary/10">
                          <AvatarFallback className="bg-primary/10 text-primary">
                            {getInitials(match.userName || "Anonymous User")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-lg font-bold">
                            {match.userName || "Anonymous User"}
                          </CardTitle>
                          <CardDescription className="text-xs flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Matched {new Date(match.timestamp || Date.now()).toLocaleDateString()}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-gray-50 rounded-lg p-3 mb-4 border border-gray-100">
                        <p className="text-xs text-gray-500 mb-1">Matched on idea:</p>
                        <p className="text-sm font-medium">{match.ideaTitle}</p>
                      </div>
                      <Button 
                        className="w-full gap-2 rounded-xl bg-primary/10 text-primary hover:bg-primary/20 hover:text-primary"
                        onClick={() => handleOpenChat(match)}
                      >
                        <MessageCircle size={16} />
                        Start Chatting
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
      
      {/* Chat Dialog */}
      <Dialog open={chatOpen} onOpenChange={setChatOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="border-b pb-3">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10 border-2 border-primary/10">
                <AvatarFallback className="bg-primary/10 text-primary">
                  {getInitials(selectedMatch?.userName || "User")}
                </AvatarFallback>
              </Avatar>
              <div>
                <DialogTitle className="text-lg font-bold">
                  {selectedMatch?.userName || "User"}
                </DialogTitle>
                <p className="text-xs text-gray-500">
                  Re: {selectedMatch?.ideaTitle}
                </p>
              </div>
            </div>
          </DialogHeader>
          
          <div className="flex flex-col h-[350px]">
            <div className="flex-1 overflow-y-auto p-3 space-y-4 mb-4 bg-gray-50 rounded-lg">
              {messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <MessageCircle className="h-12 w-12 text-gray-300 mb-3" />
                  <p className="text-gray-500 font-medium mb-1">No messages yet</p>
                  <p className="text-gray-400 text-sm max-w-[200px]">
                    Start the conversation with your potential co-founder!
                  </p>
                </div>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${
                      message.senderId === user?.id ? "justify-end" : "justify-start"
                    }`}
                  >
                    {message.senderId !== user?.id && (
                      <Avatar className="h-8 w-8 mr-2 mt-1 flex-shrink-0">
                        <AvatarFallback className="text-[10px] bg-primary/10 text-primary">
                          {getInitials(selectedMatch?.userName || "User")}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-2 shadow-sm ${
                        message.senderId === user?.id
                          ? "bg-primary text-white rounded-tr-none"
                          : "bg-white border border-gray-200 rounded-tl-none"
                      }`}
                    >
                      <p className="text-sm leading-relaxed">{message.text}</p>
                      <p className={`text-xs mt-1 text-right ${
                        message.senderId === user?.id
                          ? "text-primary-foreground/70"
                          : "text-gray-400"
                      }`}>
                        {formatTimestamp(message.timestamp)}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
            
            <DialogFooter className="flex-shrink-0">
              <div className="flex w-full gap-2 items-center">
                <Input
                  placeholder="Type your message..."
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  className="flex-1 rounded-xl"
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                />
                <Button 
                  onClick={handleSendMessage} 
                  size="icon" 
                  className="rounded-full h-10 w-10 flex-shrink-0"
                  disabled={!messageText.trim()}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
