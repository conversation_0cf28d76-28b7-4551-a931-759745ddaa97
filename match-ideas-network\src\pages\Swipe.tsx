
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useIdea } from "@/contexts/idea-context";
import { ThumbsDown, ThumbsUp, Loader2, Lightbulb, ArrowRight, Info } from "lucide-react";
import { Idea } from "@/types";
import { motion, AnimatePresence } from "framer-motion";
import { Progress } from "@/components/ui/progress";

export default function Swipe() {
  const { getSwipeableIdeas, swipeIdea } = useIdea();
  const [currentIdeas, setCurrentIdeas] = useState<Idea[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [swiping, setSwiping] = useState<"left" | "right" | null>(null);
  const [startX, setStartX] = useState(0);
  const [offsetX, setOffsetX] = useState(0);
  const [showTip, setShowTip] = useState(true);
  const cardRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const swipeThreshold = 100; // Minimum swipe distance to trigger action

  useEffect(() => {
    // Load swipeable ideas on component mount
    const ideas = getSwipeableIdeas();
    setCurrentIdeas(ideas);
    setLoading(false);
  }, [getSwipeableIdeas]);

  const handleSwipe = async (direction: "left" | "right") => {
    if (currentIndex >= currentIdeas.length) return;

    const idea = currentIdeas[currentIndex];
    setSwiping(direction);

    try {
      await swipeIdea(idea.id, direction);
      
      // Short delay for animation
      setTimeout(() => {
        setSwiping(null);
        setOffsetX(0);
        setCurrentIndex((prevIndex) => prevIndex + 1);
      }, 300);
    } catch (error) {
      console.error("Error during swipe:", error);
      setSwiping(null);
      setOffsetX(0);
    }
  };

  // Touch event handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    const currentX = e.touches[0].clientX;
    const diff = currentX - startX;
    setOffsetX(diff);
  };

  const handleTouchEnd = () => {
    if (offsetX > swipeThreshold) {
      // Swiped right
      handleSwipe("right");
    } else if (offsetX < -swipeThreshold) {
      // Swiped left
      handleSwipe("left");
    } else {
      // Reset if swipe wasn't strong enough
      setOffsetX(0);
    }
  };

  // Mouse event handlers for desktop
  const handleMouseDown = (e: React.MouseEvent) => {
    setStartX(e.clientX);
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  const handleMouseMove = (e: MouseEvent) => {
    const diff = e.clientX - startX;
    setOffsetX(diff);
  };

  const handleMouseUp = () => {
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);

    if (offsetX > swipeThreshold) {
      // Swiped right
      handleSwipe("right");
    } else if (offsetX < -swipeThreshold) {
      // Swiped left
      handleSwipe("left");
    } else {
      // Reset if swipe wasn't strong enough
      setOffsetX(0);
    }
  };

  const currentIdea = currentIdeas[currentIndex];

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <div className="relative">
            <div className="absolute inset-0 bg-primary/10 rounded-full blur-xl animate-pulse"></div>
            <Loader2 className="h-16 w-16 animate-spin mb-4 text-primary relative z-10" />
          </div>
          <p className="text-lg mt-6 font-medium">Loading ideas...</p>
          <Progress className="w-48 mt-4" value={65} />
        </div>
      );
    }

    if (currentIdeas.length === 0) {
      return (
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col items-center justify-center min-h-[60vh] max-w-md mx-auto text-center"
        >
          <div className="bg-primary/10 p-6 rounded-full mb-6">
            <Lightbulb className="h-12 w-12 text-primary" />
          </div>
          <h2 className="text-2xl font-bold mb-4">No more ideas to swipe on!</h2>
          <p className="text-muted-foreground mb-8 leading-relaxed">Check back later for new ideas or share your own brilliant concept with the community.</p>
          <Button 
            onClick={() => navigate("/submit")} 
            size="lg"
            className="gap-2 px-8 py-6 rounded-xl shadow-lg hover:shadow-xl transition-all hover:-translate-y-1"
          >
            Submit an Idea <ArrowRight size={18} />
          </Button>
        </motion.div>
      );
    }

    if (currentIndex >= currentIdeas.length) {
      return (
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col items-center justify-center min-h-[60vh] max-w-md mx-auto text-center"
        >
          <div className="bg-green-100 p-6 rounded-full mb-6">
            <ThumbsUp className="h-12 w-12 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold mb-4">You've seen all available ideas!</h2>
          <p className="text-muted-foreground mb-8 leading-relaxed">Great job! Check your dashboard for matches or submit a new idea to find more potential co-founders.</p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button 
              onClick={() => navigate("/dashboard")} 
              size="lg"
              className="gap-2 px-6 py-5 rounded-xl shadow-lg hover:shadow-xl transition-all hover:-translate-y-1"
            >
              View Dashboard
            </Button>
            <Button 
              variant="outline" 
              onClick={() => navigate("/submit")}
              size="lg"
              className="gap-2 px-6 py-5 rounded-xl hover:bg-primary/5 transition-all hover:-translate-y-1"
            >
              Submit an Idea <ArrowRight size={18} />
            </Button>
          </div>
        </motion.div>
      );
    }

    return (
      <AnimatePresence>
        <motion.div
          key={currentIdea.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, x: swiping === "left" ? -300 : swiping === "right" ? 300 : 0 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
          className="w-full max-w-md"
        >
          <Card 
            ref={cardRef}
            className={`w-full shadow-lg hover:shadow-xl transition-all duration-300 cursor-grab active:cursor-grabbing overflow-hidden border-2 ${
              offsetX > 50 ? "border-green-300" : 
              offsetX < -50 ? "border-rose-300" : "border-transparent"
            }`}
            style={{ 
              transform: offsetX ? `translateX(${offsetX}px) rotate(${offsetX * 0.05}deg)` : 'none',
              opacity: offsetX ? Math.max(1 - Math.abs(offsetX) / 500, 0.5) : 1,
              background: offsetX > 50 ? 'linear-gradient(to right, white, rgba(0, 255, 0, 0.05))' : 
                        offsetX < -50 ? 'linear-gradient(to left, white, rgba(255, 0, 0, 0.05))' : 'white'
            }}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            onMouseDown={handleMouseDown}
          >
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary/20 via-primary to-primary/20"></div>
            
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-2xl font-bold">{currentIdea.title}</CardTitle>
                <Badge className="text-xs font-medium px-3 py-1 bg-primary/10 text-primary hover:bg-primary/20">
                  {currentIdea.category}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="pb-6">
              <p className="mb-6 text-gray-600 leading-relaxed">{currentIdea.description}</p>
              <div>
                <h3 className="text-sm font-medium mb-2 flex items-center gap-1">
                  <span>Skills Needed:</span>
                </h3>
                <div className="flex flex-wrap gap-2">
                  {currentIdea.skillsNeeded.split(",").map((skill, index) => (
                    <Badge 
                      key={index} 
                      variant="outline" 
                      className="bg-gray-50 text-gray-700 hover:bg-gray-100 transition-colors"
                    >
                      {skill.trim()}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
            
            <CardFooter className="flex justify-between pt-4 border-t">
              <Button
                variant="outline"
                size="icon"
                className="h-16 w-16 rounded-full bg-rose-50 text-rose-500 hover:bg-rose-100 hover:text-rose-600 shadow-sm hover:shadow transition-all"
                onClick={() => handleSwipe("left")}
                disabled={!!swiping}
              >
                <ThumbsDown className="h-7 w-7" />
                <span className="sr-only">Pass</span>
              </Button>
              
              <div className="flex items-center text-xs text-gray-400">
                {currentIndex + 1} of {currentIdeas.length}
              </div>
              
              <Button
                variant="outline"
                size="icon"
                className="h-16 w-16 rounded-full bg-green-50 text-green-500 hover:bg-green-100 hover:text-green-600 shadow-sm hover:shadow transition-all"
                onClick={() => handleSwipe("right")}
                disabled={!!swiping}
              >
                <ThumbsUp className="h-7 w-7" />
                <span className="sr-only">Like</span>
              </Button>
        </CardFooter>
      </Card>
        </motion.div>
      </AnimatePresence>
    );
  };

  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-10"
      >
        <h1 className="text-3xl md:text-4xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">Discover Ideas</h1>
        <p className="text-gray-600 max-w-md mx-auto">Find your next co-founder by swiping on ideas that match your interests</p>
      </motion.div>
      
      <div className="flex justify-center relative">
        {offsetX > 50 && (
          <motion.div 
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="absolute top-1/2 right-8 -translate-y-1/2 z-10 bg-green-100 text-green-600 font-bold text-xl px-4 py-2 rounded-full shadow-md"
          >
            LIKE
          </motion.div>
        )}
        {offsetX < -50 && (
          <motion.div 
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="absolute top-1/2 left-8 -translate-y-1/2 z-10 bg-rose-100 text-rose-600 font-bold text-xl px-4 py-2 rounded-full shadow-md"
          >
            PASS
          </motion.div>
        )}
        {renderContent()}
      </div>
      
      {showTip && !loading && currentIndex < currentIdeas.length && (
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-10 mx-auto max-w-md bg-blue-50 border border-blue-100 rounded-xl p-4 flex items-start gap-3 relative"
        >
          <Info className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
          <div className="text-sm text-gray-700">
            <p className="font-medium mb-1">Pro Tip:</p>
            <p>Swipe right on ideas you're interested in, or left to pass. You'll be notified when there's a match!</p>
          </div>
          <button
            type="button"
            onClick={() => setShowTip(false)}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </motion.div>
      )}
      
      {!showTip && !loading && currentIndex < currentIdeas.length && (
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>Swipe right to like an idea, or left to pass</p>
        </div>
      )}
    </div>
  );
}
