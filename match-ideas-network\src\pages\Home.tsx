import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useAuth } from "@/contexts/auth-context";
import {
  Lightbulb,
  ArrowRight,
  Users,
  ThumbsUp,
  MessageSquare,
  Sparkles,
  Rocket,
  CheckCircle2,
} from "lucide-react";
import { motion, Variants } from "framer-motion";
import { AnimatedText } from "@/components/ui/animated-text";
import { AnimatedStats } from "@/components/ui/animated-stats";

const stagger = (delay: number): Variants => ({
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      delay,
    },
  },
});

export default function Home() {
  const { user } = useAuth();

  return (
    <div className="flex flex-col min-h-screen bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-blue-50/50 dark:bg-gray-900/50 py-20 md:py-28">
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-primary/5 dark:bg-primary/20 blur-3xl"
            animate={{
              x: [0, 10, -10, 5, 0],
              y: [0, -10, 10, -5, 0],
              rotate: [0, 5, -5, 2, 0],
              scale: [1, 1.05, 1, 1.02, 1],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "mirror",
            }}
          />
          <motion.div
            className="absolute top-1/2 -left-24 w-72 h-72 rounded-full bg-primary/5 dark:bg-primary/20 blur-3xl"
            animate={{
              x: [0, -10, 10, -5, 0],
              y: [0, 10, -10, 5, 0],
              rotate: [0, -5, 5, -2, 0],
              scale: [1, 1.02, 1, 1.05, 1],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "mirror",
              delay: 5,
            }}
          />
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="flex justify-center mb-6"
          >
            <div className="bg-primary text-white p-4 rounded-2xl shadow-lg">
              <Lightbulb className="h-14 w-14" />
            </div>
          </motion.div>

          <AnimatedText
            el="h1"
            text="Connect with Co-Founders for Your Next Big Idea"
            className="text-4xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600 dark:to-blue-400"
          />

          <AnimatedText
            el="p"
            text="IdeaMatch helps entrepreneurs find the perfect partners by matching on business ideas. Swipe, match, and collaborate on your next venture."
            className="text-xl text-gray-600 dark:text-gray-300 mb-10 max-w-3xl mx-auto leading-relaxed"
          />

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="flex flex-col sm:flex-row justify-center gap-4"
          >
            {user ? (
              <Link to="/swipe">
                <Button
                  size="lg"
                  className="gap-2 px-8 py-6 rounded-xl shadow-lg hover:shadow-xl transition-all hover:-translate-y-1"
                >
                  Start Swiping <ArrowRight size={18} />
                </Button>
              </Link>
            ) : (
              <>
                <Link to="/signup">
                  <Button
                    size="lg"
                    className="px-8 py-6 rounded-xl shadow-lg hover:shadow-xl transition-all hover:-translate-y-1"
                  >
                    Create Account
                  </Button>
                </Link>
                <Link to="/login">
                  <Button
                    variant="outline"
                    size="lg"
                    className="px-8 py-6 rounded-xl hover:bg-primary/5 transition-all hover:-translate-y-1 dark:border-gray-700 dark:hover:bg-primary/10"
                  >
                    Sign In
                  </Button>
                </Link>
              </>
            )}
          </motion.div>

          {/* Stats or social proof */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            transition={{ staggerChildren: 0.2 }}
            className="mt-16 flex flex-wrap justify-center gap-8 text-center"
          >
            <motion.div
              variants={stagger(0.1)}
              className="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm px-6 py-4 rounded-xl shadow-sm"
            >
              <div className="text-3xl font-bold text-primary">
                <AnimatedStats to={500} />+
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Entrepreneurs
              </div>
            </motion.div>
            <motion.div
              variants={stagger(0.2)}
              className="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm px-6 py-4 rounded-xl shadow-sm"
            >
              <div className="text-3xl font-bold text-primary">
                <AnimatedStats to={1200} />+
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Ideas Shared
              </div>
            </motion.div>
            <motion.div
              variants={stagger(0.3)}
              className="bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm px-6 py-4 rounded-xl shadow-sm"
            >
              <div className="text-3xl font-bold text-primary">
                <AnimatedStats to={300} />+
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Successful Matches
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-24 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 dark:text-white">
              How It Works
            </h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Our simple three-step process helps you find the perfect
              co-founder for your next venture
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 relative">
            <div className="hidden md:block absolute top-1/3 left-0 right-0 h-0.5 bg-gradient-to-r from-primary/20 via-primary to-primary/20 z-0" />

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stagger(0.1)}
              className="bg-white dark:bg-gray-800 rounded-2xl p-8 text-center shadow-lg border border-gray-100 dark:border-gray-700 relative z-10"
            >
              <div className="bg-primary w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-md transform -translate-y-12">
                <Lightbulb className="h-8 w-8 text-white" />
              </div>
              <span className="absolute top-4 right-4 text-xs font-medium bg-primary/10 text-primary px-2 py-1 rounded-full">
                Step 1
              </span>
              <h3 className="text-xl font-semibold mb-4 dark:text-white">
                Share Your Ideas
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Post your business concepts and describe the skills you're
                looking for in potential co-founders. Be specific about what you
                need.
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stagger(0.2)}
              className="bg-white dark:bg-gray-800 rounded-2xl p-8 text-center shadow-lg border border-gray-100 dark:border-gray-700 relative z-10 md:mt-12"
            >
              <div className="bg-primary w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-md transform -translate-y-12">
                <ThumbsUp className="h-8 w-8 text-white" />
              </div>
              <span className="absolute top-4 right-4 text-xs font-medium bg-primary/10 text-primary px-2 py-1 rounded-full">
                Step 2
              </span>
              <h3 className="text-xl font-semibold mb-4 dark:text-white">
                Swipe on Ideas
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Browse ideas from other entrepreneurs and swipe right on the
                ones that excite you. Our algorithm helps find your perfect
                match.
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stagger(0.3)}
              className="bg-white dark:bg-gray-800 rounded-2xl p-8 text-center shadow-lg border border-gray-100 dark:border-gray-700 relative z-10"
            >
              <div className="bg-primary w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-md transform -translate-y-12">
                <MessageSquare className="h-8 w-8 text-white" />
              </div>
              <span className="absolute top-4 right-4 text-xs font-medium bg-primary/10 text-primary px-2 py-1 rounded-full">
                Step 3
              </span>
              <h3 className="text-xl font-semibold mb-4 dark:text-white">
                Connect & Collaborate
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                When there's mutual interest, start a conversation and explore
                the possibility of working together on your exciting new
                venture.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 dark:text-white">
              Why Choose IdeaMatch
            </h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              We've built the perfect platform to help entrepreneurs connect and
              bring ideas to life
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stagger(0.1)}
              className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm hover:shadow-md transition-all"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Sparkles className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2 dark:text-white">
                Idea-First Matching
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Unlike other platforms that focus on people, we match based on
                business ideas and complementary skills.
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stagger(0.2)}
              className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm hover:shadow-md transition-all"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Rocket className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2 dark:text-white">
                Fast Connections
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Our intuitive swiping interface makes it quick and easy to find
                potential co-founders who share your vision.
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stagger(0.3)}
              className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm hover:shadow-md transition-all"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <CheckCircle2 className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2 dark:text-white">
                Verified Profiles
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                All users are verified to ensure you're connecting with serious
                entrepreneurs ready to collaborate.
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stagger(0.4)}
              className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm hover:shadow-md transition-all"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <MessageSquare className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2 dark:text-white">
                Secure Messaging
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Our built-in messaging system makes it easy to discuss ideas and
                plans in a secure environment.
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stagger(0.5)}
              className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm hover:shadow-md transition-all"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2 dark:text-white">
                Community Support
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Join a growing community of entrepreneurs who are ready to
                collaborate and support each other.
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stagger(0.6)}
              className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm hover:shadow-md transition-all"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Lightbulb className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2 dark:text-white">
                Idea Protection
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                We provide guidelines and resources to help protect your
                intellectual property while sharing ideas.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary to-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={stagger(0.1)}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Find Your Co-Founder?
            </h2>
            <p className="text-xl mb-10 max-w-2xl mx-auto">
              Join IdeaMatch today and connect with entrepreneurs who share your
              passion and complement your skills.
            </p>
            {user ? (
              <Link to="/submit">
                <Button
                  variant="secondary"
                  size="lg"
                  className="px-8 py-6 rounded-xl shadow-lg hover:shadow-xl transition-all hover:-translate-y-1 text-primary font-medium"
                >
                  Submit Your Idea
                </Button>
              </Link>
            ) : (
              <Link to="/signup">
                <Button variant="secondary" size="lg">
                  Get Started For Free
                </Button>
              </Link>
            )}
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 dark:bg-gray-900/50 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <Lightbulb className="h-6 w-6 mr-2" />
              <span className="font-bold text-xl">IdeaMatch</span>
            </div>
            <div className="text-gray-400 text-sm">
              © {new Date().getFullYear()} IdeaMatch. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
