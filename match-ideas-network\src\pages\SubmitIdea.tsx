
import { useState, useEffect } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useIdea } from "@/contexts/idea-context";
import { useAuth } from "@/contexts/auth-context";
import { Idea } from "@/types";
import { Lightbulb, ArrowLeft, Sparkles, Tag, Users, FileText, CheckCircle2, Loader2 } from "lucide-react";
import { motion } from "framer-motion";

export default function SubmitIdea() {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [category, setCategory] = useState<"Tech" | "Health" | "Education" | "Other">("Tech");
  const [skillsNeeded, setSkillsNeeded] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const { submitIdea } = useIdea();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // Reset form errors when inputs change
  useEffect(() => {
    setFormErrors({});
  }, [title, description, skillsNeeded]);

  const validateForm = () => {
    const errors: {[key: string]: string} = {};
    
    if (title.trim().length < 5) {
      errors.title = "Title must be at least 5 characters long";
    }
    
    if (description.trim().length < 20) {
      errors.description = "Description must be at least 20 characters long";
    }
    
    if (skillsNeeded.trim().split(',').filter(s => s.trim()).length < 1) {
      errors.skillsNeeded = "Please specify at least one skill";
    }
    
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    
    setIsSubmitting(true);

    try {
      const ideaData: Omit<Idea, "id" | "ownerId"> = {
        title: title.trim(),
        description: description.trim(),
        category,
        skillsNeeded: skillsNeeded.trim(),
        createdAt: Date.now(),
      };

      await submitIdea(ideaData);
      setSuccess(true);
      
      // Redirect after showing success message
      setTimeout(() => {
        navigate("/dashboard");
      }, 2000);
    } catch (error) {
      console.error("Error submitting idea:", error);
      setFormErrors({ submit: "Failed to submit idea. Please try again." });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="container mx-auto max-w-2xl px-4 py-12">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-6">
            <CheckCircle2 className="h-10 w-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold mb-4">Idea Submitted!</h1>
          <p className="text-gray-600 mb-8">Your idea has been successfully submitted. Redirecting to dashboard...</p>
          <div className="flex justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        </motion.div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto max-w-3xl px-4 py-12">
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-6"
      >
        <Button 
          variant="ghost" 
          onClick={() => navigate(-1)} 
          className="mb-4 hover:bg-transparent hover:text-primary"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <h1 className="text-3xl md:text-4xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">Submit Your Idea</h1>
        <p className="text-gray-600 max-w-2xl">Share your business concept and find the perfect co-founder with complementary skills</p>
      </motion.div>
      
      <div className="grid md:grid-cols-3 gap-6">
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="md:col-span-2"
        >
          <Card className="border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="border-b pb-4">
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 p-2 rounded-lg">
                  <Lightbulb className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-xl">Idea Details</CardTitle>
                  <CardDescription>
                    Provide information about your business concept
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <form onSubmit={handleSubmit} className="space-y-6">
            {formErrors.submit && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{formErrors.submit}</AlertDescription>
              </Alert>
            )}
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="title" className="flex items-center gap-1">
                  <Tag className="h-3.5 w-3.5" />
                  Title
                </Label>
                <p className="text-xs text-muted-foreground">
                  {title.length}/50 characters
                </p>
              </div>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                maxLength={50}
                placeholder="A concise name for your idea"
                className={formErrors.title ? "border-red-300 focus-visible:ring-red-300" : ""}
                required
              />
              {formErrors.title && (
                <p className="text-xs text-red-500 mt-1">{formErrors.title}</p>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="description" className="flex items-center gap-1">
                  <FileText className="h-3.5 w-3.5" />
                  Description
                </Label>
                <p className="text-xs text-muted-foreground">
                  {description.length}/200 characters
                </p>
              </div>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                maxLength={200}
                placeholder="Briefly describe your business idea"
                className={`resize-none h-32 ${formErrors.description ? "border-red-300 focus-visible:ring-red-300" : ""}`}
                required
              />
              {formErrors.description && (
                <p className="text-xs text-red-500 mt-1">{formErrors.description}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="category" className="flex items-center gap-1">
                <Sparkles className="h-3.5 w-3.5" />
                Category
              </Label>
              <Select
                value={category}
                onValueChange={(value) =>
                  setCategory(value as "Tech" | "Health" | "Education" | "Other")
                }
              >
                <SelectTrigger id="category" className="bg-white">
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Tech">Tech</SelectItem>
                  <SelectItem value="Health">Health</SelectItem>
                  <SelectItem value="Education">Education</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="skills" className="flex items-center gap-1">
                <Users className="h-3.5 w-3.5" />
                Skills Needed
              </Label>
              <Input
                id="skills"
                value={skillsNeeded}
                onChange={(e) => setSkillsNeeded(e.target.value)}
                placeholder="e.g., Developer, Designer, Marketing"
                className={formErrors.skillsNeeded ? "border-red-300 focus-visible:ring-red-300" : ""}
                required
              />
              {formErrors.skillsNeeded ? (
                <p className="text-xs text-red-500 mt-1">{formErrors.skillsNeeded}</p>
              ) : (
                <p className="text-xs text-muted-foreground">
                  Separate skills with commas
                </p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full rounded-xl py-5 mt-4 shadow-md hover:shadow-lg transition-all hover:-translate-y-1"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                "Submit Idea"
              )}
            </Button>
          </form>
            </CardContent>
          </Card>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="md:col-span-1"
        >
          <Card className="bg-gray-50 border-gray-200 h-full">
            <CardHeader>
              <CardTitle className="text-lg">Tips for Success</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                <h3 className="font-medium text-sm mb-1 flex items-center gap-1">
                  <span className="text-primary">•</span> Be Specific
                </h3>
                <p className="text-xs text-gray-600">Clearly describe your idea and the problem it solves.</p>
              </div>
              
              <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                <h3 className="font-medium text-sm mb-1 flex items-center gap-1">
                  <span className="text-primary">•</span> List Required Skills
                </h3>
                <p className="text-xs text-gray-600">Specify the exact skills you're looking for in a co-founder.</p>
              </div>
              
              <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                <h3 className="font-medium text-sm mb-1 flex items-center gap-1">
                  <span className="text-primary">•</span> Choose the Right Category
                </h3>
                <p className="text-xs text-gray-600">This helps your idea reach the right potential co-founders.</p>
              </div>
              
              <div className="mt-6 pt-4 border-t border-gray-200">
                <p className="text-xs text-gray-500">After submission, your idea will be visible to potential co-founders who can express interest by swiping right.</p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
