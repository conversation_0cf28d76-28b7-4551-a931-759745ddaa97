import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Idea, Swipe, Match, Message, User } from "@/types";
import { useAuth } from "./auth-context";

interface IdeaContextProps {
  ideas: Idea[];
  userIdeas: Idea[];
  swipes: Swipe[];
  matches: Match[];
  messages: Record<string, Message[]>;
  likedIdeas: Idea[];
  loading: boolean;
  submitIdea: (idea: Omit<Idea, "id" | "ownerId">) => Promise<void>;
  swipeIdea: (ideaId: string, direction: "left" | "right") => Promise<void>;
  sendMessage: (matchId: string, text: string) => Promise<void>;
  getMessagesForMatch: (matchId: string) => Message[];
  getSwipeableIdeas: () => Idea[];
  getUserConnections: () => User[];
}

const IdeaContext = createContext<IdeaContextProps | undefined>(undefined);

// Mock database
const mockIdeas: Idea[] = [
  {
    id: "1",
    title: "AI Writing Assistant",
    description: "An AI-powered tool that helps writers overcome writer's block and create better content.",
    category: "Tech",
    skillsNeeded: "AI Engineer, Content Writer, UI Designer",
    ownerId: "2",
    ownerName: "John Smith"
  },
  {
    id: "2",
    title: "Eco-friendly Food Delivery",
    description: "A food delivery service using only eco-friendly packaging and electric vehicles.",
    category: "Other",
    skillsNeeded: "Logistics Expert, Sustainability Specialist",
    ownerId: "3",
    ownerName: "Emma Green"
  },
  {
    id: "3",
    title: "Health Monitoring Wearable",
    description: "A wearable device that monitors health vitals and provides personalized recommendations.",
    category: "Health",
    skillsNeeded: "Hardware Engineer, Health Specialist, Mobile Developer",
    ownerId: "4",
    ownerName: "Michael Health"
  },
  {
    id: "4",
    title: "Interactive Learning Platform",
    description: "An interactive platform for students to learn complex subjects through gamification.",
    category: "Education",
    skillsNeeded: "Education Expert, Game Developer, UI/UX Designer",
    ownerId: "5",
    ownerName: "Sarah Teach"
  },
  {
    id: "5",
    title: "Local Artisan Marketplace",
    description: "A platform connecting local artisans with customers looking for unique, handcrafted products.",
    category: "Other",
    skillsNeeded: "Marketing Specialist, Full-stack Developer",
    ownerId: "6",
    ownerName: "David Craft"
  }
];

const mockSwipes: Swipe[] = [];
const mockMatches: Match[] = [];
const mockMessages: Record<string, Message[]> = {};

// LocalStorage keys
const IDEAS_STORAGE_KEY = 'ideamatch_ideas';
const SWIPES_STORAGE_KEY = 'ideamatch_swipes';
const MATCHES_STORAGE_KEY = 'ideamatch_matches';
const MESSAGES_STORAGE_KEY = 'ideamatch_messages';

export function IdeaProvider({ children }: { children: ReactNode }) {
  const [ideas, setIdeas] = useState<Idea[]>([]);
  const [swipes, setSwipes] = useState<Swipe[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [messages, setMessages] = useState<Record<string, Message[]>>({});
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    // Initialize from localStorage or fallback to mock data
    const loadData = () => {
      const storedIdeas = localStorage.getItem(IDEAS_STORAGE_KEY);
      const storedSwipes = localStorage.getItem(SWIPES_STORAGE_KEY);
      const storedMatches = localStorage.getItem(MATCHES_STORAGE_KEY);
      const storedMessages = localStorage.getItem(MESSAGES_STORAGE_KEY);

      setIdeas(storedIdeas ? JSON.parse(storedIdeas) : mockIdeas);
      setSwipes(storedSwipes ? JSON.parse(storedSwipes) : mockSwipes);
      setMatches(storedMatches ? JSON.parse(storedMatches) : mockMatches);
      setMessages(storedMessages ? JSON.parse(storedMessages) : mockMessages);
      setLoading(false);
    };

    loadData();
  }, []);

  // Save data to localStorage whenever it changes
  useEffect(() => {
    if (!loading) {
      localStorage.setItem(IDEAS_STORAGE_KEY, JSON.stringify(ideas));
      localStorage.setItem(SWIPES_STORAGE_KEY, JSON.stringify(swipes));
      localStorage.setItem(MATCHES_STORAGE_KEY, JSON.stringify(matches));
      localStorage.setItem(MESSAGES_STORAGE_KEY, JSON.stringify(messages));
    }
  }, [ideas, swipes, matches, messages, loading]);

  const userIdeas = user ? ideas.filter(idea => idea.ownerId === user.id) : [];
  
  const likedIdeas = user 
    ? ideas.filter(idea => 
        swipes.some(swipe => 
          swipe.userId === user.id && 
          swipe.ideaId === idea.id && 
          swipe.direction === 'right'
        )
      ) 
    : [];

  const submitIdea = async (ideaData: Omit<Idea, "id" | "ownerId">) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please login to submit an idea",
        variant: "destructive",
      });
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newIdea: Idea = {
        ...ideaData,
        id: (ideas.length + 1).toString(),
        ownerId: user.id,
        ownerName: user.name,
      };
      
      setIdeas(prev => [...prev, newIdea]);
      toast({
        title: "Idea submitted",
        description: "Your idea has been submitted successfully",
      });
    } catch (error) {
      toast({
        title: "Submission failed",
        description: "Failed to submit your idea",
        variant: "destructive",
      });
    }
  };

  const swipeIdea = async (ideaId: string, direction: "left" | "right") => {
    if (!user) return;

    try {
      // Record the swipe
      const newSwipe: Swipe = {
        userId: user.id,
        ideaId,
        direction,
      };
      
      setSwipes(prev => [...prev, newSwipe]);
      
      // Check for matches if swipe direction is right
      if (direction === 'right') {
        const idea = ideas.find(i => i.id === ideaId);
        if (!idea) return;
        
        // Find if the idea owner has liked any of current user's ideas
        const ownerSwipes = swipes.filter(s => 
          s.userId === idea.ownerId && 
          s.direction === 'right'
        );
        
        const userIdeas = ideas.filter(i => i.ownerId === user.id);
        
        for (const userIdea of userIdeas) {
          const hasMatch = ownerSwipes.some(s => s.ideaId === userIdea.id);
          
          if (hasMatch) {
            // Create a match
            const newMatch: Match = {
              id: `${user.id}-${idea.ownerId}-${ideaId}-${userIdea.id}`,
              user1Id: user.id,
              user2Id: idea.ownerId,
              ideaId: ideaId,
              ideaTitle: idea.title,
              userName: idea.ownerName,
            };
            
            setMatches(prev => [...prev, newMatch]);
            
            toast({
              title: "New match!",
              description: `You matched with ${idea.ownerName || 'someone'} on "${idea.title}"`,
            });
            
            // Initialize empty message array for this match
            setMessages(prev => ({
              ...prev,
              [newMatch.id]: [],
            }));
            
            break;
          }
        }
      }
    } catch (error) {
      console.error("Error processing swipe:", error);
    }
  };

  const sendMessage = async (matchId: string, text: string) => {
    if (!user) return;

    try {
      const match = matches.find(m => m.id === matchId);
      if (!match) return;

      // Determine receiver ID (the other user in the match)
      const receiverId = match.user1Id === user.id ? match.user2Id : match.user1Id;

      const newMessage: Message = {
        id: `msg-${Date.now()}`,
        senderId: user.id,
        receiverId,
        text,
        timestamp: Date.now(),
      };

      // Add message to the match's messages
      setMessages(prev => ({
        ...prev,
        [matchId]: [...(prev[matchId] || []), newMessage],
      }));

      return;
    } catch (error) {
      console.error("Error sending message:", error);
      toast({
        title: "Failed to send message",
        description: "Please try again later",
        variant: "destructive",
      });
    }
  };

  const getMessagesForMatch = (matchId: string) => {
    return messages[matchId] || [];
  };

  const getSwipeableIdeas = () => {
    if (!user) return [];
    
    // Filter ideas that:
    // 1. Don't belong to the current user
    // 2. Haven't been swiped on yet by the current user
    const swipedIdeaIds = swipes
      .filter(swipe => swipe.userId === user.id)
      .map(swipe => swipe.ideaId);
    
    return ideas.filter(
      idea => idea.ownerId !== user.id && !swipedIdeaIds.includes(idea.id)
    );
  };

  const getUserConnections = (): User[] => {
    if (!user) return [];
    
    // Get all users who are connected through matches
    const userMatches = matches.filter(
      m => m.user1Id === user.id || m.user2Id === user.id
    );
    
    // Mock the existence of other users in the system
    // In a real app, this would come from an API
    const mockUsers: Record<string, User> = {
      "2": {
        id: "2",
        email: "<EMAIL>",
        name: "John Smith",
        skills: "AI Engineer, Content Writer, UI Designer",
      },
      "3": {
        id: "3",
        email: "<EMAIL>",
        name: "Emma Green",
        skills: "Logistics Expert, Sustainability Specialist",
      },
      "4": {
        id: "4",
        email: "<EMAIL>",
        name: "Michael Health",
        skills: "Hardware Engineer, Health Specialist, Mobile Developer",
      },
      "5": {
        id: "5",
        email: "<EMAIL>",
        name: "Sarah Teach",
        skills: "Education Expert, Game Developer, UI/UX Designer",
      },
      "6": {
        id: "6",
        email: "<EMAIL>",
        name: "David Craft",
        skills: "Marketing Specialist, Full-stack Developer",
      }
    };
    
    // Get unique user IDs from matches
    const connectedUserIds = Array.from(new Set(
      userMatches.map(m => 
        m.user1Id === user.id ? m.user2Id : m.user1Id
      )
    ));
    
    // Return user objects for each connected user
    return connectedUserIds
      .map(id => mockUsers[id])
      .filter(Boolean);
  };

  return (
    <IdeaContext.Provider
      value={{
        ideas,
        userIdeas,
        swipes,
        matches,
        messages,
        likedIdeas,
        loading,
        submitIdea,
        swipeIdea,
        sendMessage,
        getMessagesForMatch,
        getSwipeableIdeas,
        getUserConnections,
      }}
    >
      {children}
    </IdeaContext.Provider>
  );
}

export function useIdea() {
  const context = useContext(IdeaContext);
  if (context === undefined) {
    throw new Error("useIdea must be used within an IdeaProvider");
  }
  return context;
}
