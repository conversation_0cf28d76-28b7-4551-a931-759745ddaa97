{"version": 3, "sources": ["../src/primitive.tsx"], "sourcesContent": ["/* eslint-disable no-restricted-properties */\n\n/* eslint-disable no-restricted-globals */\nexport const canUseDOM = !!(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n/* eslint-enable no-restricted-globals */\n\nexport function composeEventHandlers<E extends { defaultPrevented: boolean }>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport function getOwnerWindow(element: Node | null | undefined) {\n  if (!canUseDOM) {\n    throw new Error('Cannot access window outside of the DOM');\n  }\n  // eslint-disable-next-line no-restricted-globals\n  return element?.ownerDocument?.defaultView ?? window;\n}\n\nexport function getOwnerDocument(element: Node | null | undefined) {\n  if (!canUseDOM) {\n    throw new Error('Cannot access document outside of the DOM');\n  }\n  // eslint-disable-next-line no-restricted-globals\n  return element?.ownerDocument ?? document;\n}\n\n/**\n * Lifted from https://github.com/ariakit/ariakit/blob/main/packages/ariakit-core/src/utils/dom.ts#L37\n * MIT License, Copyright (c) AriaKit.\n */\nexport function getActiveElement(\n  node: Node | null | undefined,\n  activeDescendant = false\n): HTMLElement | null {\n  const { activeElement } = getOwnerDocument(node);\n  if (!activeElement?.nodeName) {\n    // `activeElement` might be an empty object if we're interacting with elements\n    // inside of an iframe.\n    return null;\n  }\n\n  if (isFrame(activeElement) && activeElement.contentDocument) {\n    return getActiveElement(activeElement.contentDocument.body, activeDescendant);\n  }\n\n  if (activeDescendant) {\n    const id = activeElement.getAttribute('aria-activedescendant');\n    if (id) {\n      const element = getOwnerDocument(activeElement).getElementById(id);\n      if (element) {\n        return element;\n      }\n    }\n  }\n\n  return activeElement as HTMLElement | null;\n}\n\nexport function isFrame(element: Element): element is HTMLIFrameElement {\n  return element.tagName === 'IFRAME';\n}\n"], "mappings": ";AAGO,IAAM,YAAY,CAAC,EACxB,OAAO,WAAW,eAClB,OAAO,YACP,OAAO,SAAS;AAIX,SAAS,qBACd,sBACA,iBACA,EAAE,2BAA2B,KAAK,IAAI,CAAC,GACvC;AACA,SAAO,SAAS,YAAY,OAAU;AACpC,2BAAuB,KAAK;AAE5B,QAAI,6BAA6B,SAAS,CAAC,MAAM,kBAAkB;AACjE,aAAO,kBAAkB,KAAK;AAAA,IAChC;AAAA,EACF;AACF;AAEO,SAAS,eAAe,SAAkC;AAC/D,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AAEA,SAAO,SAAS,eAAe,eAAe;AAChD;AAEO,SAAS,iBAAiB,SAAkC;AACjE,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC7D;AAEA,SAAO,SAAS,iBAAiB;AACnC;AAMO,SAAS,iBACd,MACA,mBAAmB,OACC;AACpB,QAAM,EAAE,cAAc,IAAI,iBAAiB,IAAI;AAC/C,MAAI,CAAC,eAAe,UAAU;AAG5B,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,aAAa,KAAK,cAAc,iBAAiB;AAC3D,WAAO,iBAAiB,cAAc,gBAAgB,MAAM,gBAAgB;AAAA,EAC9E;AAEA,MAAI,kBAAkB;AACpB,UAAM,KAAK,cAAc,aAAa,uBAAuB;AAC7D,QAAI,IAAI;AACN,YAAM,UAAU,iBAAiB,aAAa,EAAE,eAAe,EAAE;AACjE,UAAI,SAAS;AACX,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,QAAQ,SAAgD;AACtE,SAAO,QAAQ,YAAY;AAC7B;", "names": []}